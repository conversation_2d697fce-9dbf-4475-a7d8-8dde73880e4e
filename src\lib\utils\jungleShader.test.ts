import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock PIXI.js with shader-specific functionality
const mockShader = {
    uniforms: {
        uTime: 0,
        uResolution: [800, 600],
    },
};

const mockMesh = {
    position: { set: vi.fn() },
    scale: { set: vi.fn() },
    width: 800,
    height: 600,
    zIndex: 0,
    destroy: vi.fn(),
};

const mockGeometry = {
    addAttribute: vi.fn(),
    addIndex: vi.fn(),
};

const mockGraphics = {
    beginFill: vi.fn().mockReturnThis(),
    drawRect: vi.fn().mockReturnThis(),
    endFill: vi.fn().mockReturnThis(),
    destroy: vi.fn(),
    x: 0,
    y: 0,
    width: 800,
    height: 600,
    zIndex: -1000,
};

vi.mock('pixi.js', () => ({
    Shader: {
        from: vi.fn(() => mockShader),
    },
    Mesh: vi.fn(() => mockMesh),
    Geometry: vi.fn(() => mockGeometry),
    Graphics: vi.fn(() => mockGraphics),
}));

// Import after mocks are set up
import { createJungleShader } from './jungleShader';

describe('Jungle Shader System', () => {
    let mockApp: any;
    let consoleWarnSpy: any;

    beforeEach(() => {
        mockApp = {
            screen: {
                width: 800,
                height: 600,
            },
        };

        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
    });

    afterEach(() => {
        vi.clearAllMocks();
        consoleWarnSpy.mockRestore();
    });

    describe('Shader Creation', () => {
        it('should create shader with proper uniforms', () => {
            const PIXI = require('pixi.js');
            const shader = createJungleShader(mockApp);

            expect(PIXI.Shader.from).toHaveBeenCalled();
            expect(PIXI.Mesh).toHaveBeenCalled();
            expect(shader.mesh).toBeDefined();
            expect(shader.update).toBeDefined();
            expect(shader.resize).toBeDefined();
            expect(shader.destroy).toBeDefined();
        });

        it('should set proper mesh properties', () => {
            const shader = createJungleShader(mockApp);

            expect(shader.mesh.position.set).toHaveBeenCalledWith(0, 0);
            expect(shader.mesh.width).toBe(800);
            expect(shader.mesh.height).toBe(600);
            expect(shader.mesh.zIndex).toBe(-1000);
        });

        it('should handle shader creation failure with fallback', () => {
            const PIXI = require('pixi.js');

            // Mock shader creation to fail
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => {
                throw new Error('WebGL context lost');
            });

            const shader = createJungleShader(mockApp);

            expect(consoleWarnSpy).toHaveBeenCalledWith(
                'Failed to create jungle shader, falling back to simple background:',
                expect.any(Error)
            );

            // Should return fallback graphics
            expect(shader.mesh).toBeDefined();
            expect(shader.update).toBeDefined();
            expect(shader.resize).toBeDefined();
            expect(shader.destroy).toBeDefined();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });
    });

    describe('Shader Updates', () => {
        it('should update shader uniforms with delta time', () => {
            const PIXI = require('pixi.js');
            const mockShader = {
                uniforms: {
                    uTime: 0,
                    uResolution: [800, 600],
                },
            };
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => mockShader);

            const shader = createJungleShader(mockApp);
            const initialTime = mockShader.uniforms.uTime;

            shader.update(16.67); // ~60fps delta

            expect(mockShader.uniforms.uTime).toBeGreaterThan(initialTime);

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });

        it('should handle update when shader uniforms are undefined', () => {
            const PIXI = require('pixi.js');
            const mockShader = {
                uniforms: undefined,
            };
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => mockShader);

            const shader = createJungleShader(mockApp);

            // Should not throw when uniforms are undefined
            expect(() => shader.update(16.67)).not.toThrow();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });

        it('should handle update for fallback graphics', () => {
            const PIXI = require('pixi.js');

            // Mock shader creation to fail, triggering fallback
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Fallback update should be a no-op and not throw
            expect(() => shader.update(16.67)).not.toThrow();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });
    });

    describe('Shader Resize', () => {
        it('should update resolution uniforms on resize', () => {
            const PIXI = require('pixi.js');
            const mockShader = {
                uniforms: {
                    uTime: 0,
                    uResolution: [800, 600],
                },
            };
            const mockMesh = {
                position: { set: vi.fn() },
                width: 800,
                height: 600,
                zIndex: -1000,
                destroy: vi.fn(),
            };

            const originalFrom = PIXI.Shader.from;
            const originalMesh = PIXI.Mesh;
            PIXI.Shader.from = vi.fn(() => mockShader);
            PIXI.Mesh = vi.fn(() => mockMesh);

            const shader = createJungleShader(mockApp);

            // Change app screen size
            mockApp.screen.width = 1200;
            mockApp.screen.height = 800;

            shader.resize();

            expect(mockShader.uniforms.uResolution).toEqual([1200, 800]);
            expect(mockMesh.position.set).toHaveBeenCalledWith(0, 0);
            expect(mockMesh.width).toBe(1200);
            expect(mockMesh.height).toBe(800);

            // Restore original mocks
            PIXI.Shader.from = originalFrom;
            PIXI.Mesh = originalMesh;
        });

        it('should handle resize when shader uniforms are undefined', () => {
            const PIXI = require('pixi.js');
            const mockShader = {
                uniforms: undefined,
            };
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => mockShader);

            const shader = createJungleShader(mockApp);

            // Should not throw when uniforms are undefined
            expect(() => shader.resize()).not.toThrow();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });

        it('should handle resize for fallback graphics', () => {
            const PIXI = require('pixi.js');

            // Mock shader creation to fail, triggering fallback
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Fallback resize should recreate the graphics
            expect(() => shader.resize()).not.toThrow();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });
    });

    describe('Shader Cleanup', () => {
        it('should destroy shader mesh', () => {
            const shader = createJungleShader(mockApp);

            shader.destroy();

            expect(shader.mesh.destroy).toHaveBeenCalled();
        });

        it('should handle destroy for fallback graphics', () => {
            const PIXI = require('pixi.js');

            // Mock shader creation to fail, triggering fallback
            const originalFrom = PIXI.Shader.from;
            PIXI.Shader.from = vi.fn(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Should not throw when destroying fallback
            expect(() => shader.destroy()).not.toThrow();

            // Restore original mock
            PIXI.Shader.from = originalFrom;
        });
    });

    describe('Shader Code Validation', () => {
        it('should create shader with vertex and fragment shaders', () => {
            const PIXI = require('pixi.js');
            createJungleShader(mockApp);

            const shaderCall = PIXI.Shader.from.mock.calls[0];
            expect(shaderCall).toHaveLength(3); // vertex, fragment, uniforms

            const [vertexShader, fragmentShader, uniforms] = shaderCall;

            // Verify shader strings contain expected content
            expect(typeof vertexShader).toBe('string');
            expect(typeof fragmentShader).toBe('string');
            expect(vertexShader).toContain('attribute vec2 aVertexPosition');
            expect(fragmentShader).toContain('gl_FragColor');

            // Verify uniforms
            expect(uniforms).toHaveProperty('uTime');
            expect(uniforms).toHaveProperty('uResolution');
            expect(uniforms.uTime).toBe(0.0);
            expect(uniforms.uResolution).toEqual([800, 600]);
        });
    });
});
