import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock PIXI.js with shader-specific functionality
const mockShader = {
    uniforms: {
        uTime: 0,
        uResolution: [800, 600],
    },
};

const mockMesh = {
    position: { set: vi.fn() },
    scale: { set: vi.fn() },
    width: 800,
    height: 600,
    zIndex: 0,
    destroy: vi.fn(),
};

const mockGeometry = {
    addAttribute: vi.fn(),
    addIndex: vi.fn(),
};

const mockGraphics = {
    beginFill: vi.fn().mockReturnThis(),
    drawRect: vi.fn().mockReturnThis(),
    endFill: vi.fn().mockReturnThis(),
    clear: vi.fn().mockReturnThis(),
    destroy: vi.fn(),
    x: 0,
    y: 0,
    width: 800,
    height: 600,
    zIndex: -1000,
};

vi.mock('pixi.js', () => ({
    Shader: {
        from: vi.fn(() => mockShader),
    },
    Mesh: vi.fn(() => mockMesh),
    Geometry: vi.fn(() => mockGeometry),
    Graphics: vi.fn(() => mockGraphics),
}));

// Import after mocks are set up
import { createJungleShader } from './jungleShader';
import * as PIXI from 'pixi.js';

// Get references to the mocked functions
const mockShaderFrom = PIXI.Shader.from as any;
const mockMeshConstructor = PIXI.Mesh as any;
const mockGeometryConstructor = PIXI.Geometry as any;
const mockGraphicsConstructor = PIXI.Graphics as any;

describe('Jungle Shader System', () => {
    let mockApp: any;
    let consoleWarnSpy: any;

    beforeEach(() => {
        vi.clearAllMocks();

        // Reset mock state
        mockShader.uniforms.uTime = 0;
        mockShader.uniforms.uResolution = [800, 600];
        mockMesh.width = 800;
        mockMesh.height = 600;

        mockApp = {
            screen: {
                width: 800,
                height: 600,
            },
        };

        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
    });

    afterEach(() => {
        vi.clearAllMocks();
        consoleWarnSpy.mockRestore();
    });

    describe('Shader Creation', () => {
        it('should create shader with proper uniforms', () => {
            const shader = createJungleShader(mockApp);

            expect(mockShaderFrom).toHaveBeenCalled();
            expect(mockMeshConstructor).toHaveBeenCalled();
            expect(shader.mesh).toBeDefined();
            expect(shader.update).toBeDefined();
            expect(shader.resize).toBeDefined();
            expect(shader.destroy).toBeDefined();
        });

        it('should set proper mesh properties', () => {
            const shader = createJungleShader(mockApp);

            expect(shader.mesh.position.set).toHaveBeenCalledWith(0, 0);
            expect(shader.mesh.width).toBe(800);
            expect(shader.mesh.height).toBe(600);
            expect(shader.mesh.zIndex).toBe(-1000);
        });

        it('should handle shader creation failure with fallback', () => {
            // Mock shader creation to fail
            mockShaderFrom.mockImplementationOnce(() => {
                throw new Error('WebGL context lost');
            });

            const shader = createJungleShader(mockApp);

            expect(consoleWarnSpy).toHaveBeenCalledWith(
                'Failed to create jungle shader, falling back to simple background:',
                expect.any(Error)
            );

            // Should return fallback graphics
            expect(shader.mesh).toBeDefined();
            expect(shader.update).toBeDefined();
            expect(shader.resize).toBeDefined();
            expect(shader.destroy).toBeDefined();
        });
    });

    describe('Shader Updates', () => {
        it('should update shader uniforms with delta time', () => {
            const shader = createJungleShader(mockApp);
            const initialTime = mockShader.uniforms.uTime;

            shader.update(16.67); // ~60fps delta

            expect(mockShader.uniforms.uTime).toBeGreaterThan(initialTime);
        });

        it('should handle update when shader uniforms are undefined', () => {
            const localMockShader = {
                uniforms: undefined,
            };
            mockShaderFrom.mockImplementationOnce(() => localMockShader);

            const shader = createJungleShader(mockApp);

            // Should not throw when uniforms are undefined
            expect(() => shader.update(16.67)).not.toThrow();
        });

        it('should handle update for fallback graphics', () => {
            // Mock shader creation to fail, triggering fallback
            mockShaderFrom.mockImplementationOnce(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Fallback update should be a no-op and not throw
            expect(() => shader.update(16.67)).not.toThrow();
        });
    });

    describe('Shader Resize', () => {
        it('should update resolution uniforms on resize', () => {
            const shader = createJungleShader(mockApp);

            // Change app screen size
            mockApp.screen.width = 1200;
            mockApp.screen.height = 800;

            shader.resize();

            expect(mockShader.uniforms.uResolution).toEqual([1200, 800]);
            expect(mockMesh.position.set).toHaveBeenCalledWith(0, 0);
            expect(mockMesh.width).toBe(1200);
            expect(mockMesh.height).toBe(800);
        });

        it('should handle resize when shader uniforms are undefined', () => {
            const localMockShader = {
                uniforms: undefined,
            };
            mockShaderFrom.mockImplementationOnce(() => localMockShader);

            const shader = createJungleShader(mockApp);

            // Should not throw when uniforms are undefined
            expect(() => shader.resize()).not.toThrow();
        });

        it('should handle resize for fallback graphics', () => {
            // Mock shader creation to fail, triggering fallback
            mockShaderFrom.mockImplementationOnce(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Fallback resize should recreate the graphics
            expect(() => shader.resize()).not.toThrow();
        });
    });

    describe('Shader Cleanup', () => {
        it('should destroy shader mesh', () => {
            const shader = createJungleShader(mockApp);

            shader.destroy();

            expect(shader.mesh.destroy).toHaveBeenCalled();
        });

        it('should handle destroy for fallback graphics', () => {
            // Mock shader creation to fail, triggering fallback
            mockShaderFrom.mockImplementationOnce(() => {
                throw new Error('WebGL not supported');
            });

            const shader = createJungleShader(mockApp);

            // Should not throw when destroying fallback
            expect(() => shader.destroy()).not.toThrow();
        });
    });

    describe('Shader Code Validation', () => {
        it('should create shader with vertex and fragment shaders', () => {
            createJungleShader(mockApp);

            const shaderCall = mockShaderFrom.mock.calls[0];
            expect(shaderCall).toHaveLength(3); // vertex, fragment, uniforms

            const [vertexShader, fragmentShader, uniforms] = shaderCall;

            // Verify shader strings contain expected content
            expect(typeof vertexShader).toBe('string');
            expect(typeof fragmentShader).toBe('string');
            expect(vertexShader).toContain('attribute vec2 aVertexPosition');
            expect(fragmentShader).toContain('gl_FragColor');

            // Verify uniforms
            expect(uniforms).toHaveProperty('uTime');
            expect(uniforms).toHaveProperty('uResolution');
            expect(uniforms.uTime).toBe(0.0);
            expect(uniforms.uResolution).toEqual([800, 600]);
        });
    });
});
