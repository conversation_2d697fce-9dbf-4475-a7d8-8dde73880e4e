import * as PIXI from 'pixi.js';

/**
 * Creates a shader-based atmospheric effect for the jungle background
 * This adds a subtle animated noise pattern and depth fog
 */
export function createJungleShader(app: PIXI.Application) {
    // Create a simple quad geometry
    const geometry = new PIXI.Geometry();

    // Define vertices for a fullscreen quad
    const vertices = [
        -1, -1,  // Bottom left
        1, -1,  // Bottom right
        1, 1,  // Top right
        -1, 1   // Top left
    ];

    // Define UV coordinates
    const uvs = [
        0, 1,  // Bottom left
        1, 1,  // Bottom right
        1, 0,  // Top right
        0, 0   // Top left
    ];

    // Define indices for two triangles
    const indices = [0, 1, 2, 0, 2, 3];

    geometry.addAttribute('aVertexPosition', vertices, 2);
    geometry.addAttribute('aTextureCoord', uvs, 2);
    geometry.addIndex(indices);

    // Simple vertex shader
    const vertexShader = `
    attribute vec2 aVertexPosition;
    attribute vec2 aTextureCoord;
    varying vec2 vTextureCoord;
    
    void main() {
      vTextureCoord = aTextureCoord;
      gl_Position = vec4(aVertexPosition, 0.0, 1.0);
    }
  `;

    // Fragment shader with atmospheric effects
    const fragmentShader = `
    precision mediump float;
    varying vec2 vTextureCoord;
    uniform float uTime;
    uniform vec2 uResolution;
    
    // Simple noise function
    float noise(vec2 p) {
      return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
    }
    
    // Fractal noise
    float fbm(vec2 p) {
      float value = 0.0;
      float amplitude = 0.5;
      for(int i = 0; i < 4; i++) {
        value += amplitude * noise(p);
        p *= 2.0;
        amplitude *= 0.5;
      }
      return value;
    }
    
    void main() {
      vec2 uv = vTextureCoord;
      
      // Create moving noise pattern
      vec2 noiseCoord = uv * 3.0 + vec2(uTime * 0.1, uTime * 0.05);
      float noiseValue = fbm(noiseCoord);
      
      // Create depth fog effect (darker at top, lighter at bottom)
      float depthFog = smoothstep(0.0, 1.0, uv.y) * 0.3;
      
      // Combine effects
      float atmosphere = noiseValue * 0.1 + depthFog;
      
      // Dark green atmospheric color
      vec3 atmosphereColor = vec3(0.1, 0.2, 0.1) * atmosphere;
      
      gl_FragColor = vec4(atmosphereColor, atmosphere * 0.5);
    }
  `;

    // Create shader with error handling
    let shader: PIXI.Shader;
    let mesh: PIXI.Mesh;

    try {
        shader = PIXI.Shader.from(vertexShader, fragmentShader, {
            uTime: 0.0,
            uResolution: [app.screen.width, app.screen.height]
        });

        // Create mesh
        mesh = new PIXI.Mesh(geometry, shader);
        mesh.position.set(0, 0);
        mesh.width = app.screen.width;
        mesh.height = app.screen.height;

        // Ensure it renders behind everything
        mesh.zIndex = -1000;
    } catch (error) {
        console.warn('Failed to create jungle shader, falling back to simple background:', error);

        // Create a simple fallback graphics instead of shader
        const fallbackGraphics = new PIXI.Graphics();
        fallbackGraphics.beginFill(0x0a1f14, 0.3);
        fallbackGraphics.drawRect(0, 0, app.screen.width, app.screen.height);
        fallbackGraphics.endFill();
        fallbackGraphics.zIndex = -1000;

        return {
            mesh: fallbackGraphics,
            update: () => { }, // No-op for fallback
            resize: () => {
                fallbackGraphics.clear();
                fallbackGraphics.beginFill(0x0a1f14, 0.3);
                fallbackGraphics.drawRect(0, 0, app.screen.width, app.screen.height);
                fallbackGraphics.endFill();
            },
            destroy: () => {
                fallbackGraphics.destroy();
            }
        };
    }

    // Update function
    function updateShader(deltaTime: number) {
        if (shader && shader.uniforms) {
            shader.uniforms.uTime += deltaTime * 0.016;
        }
    }

    // Resize function
    function resizeShader() {
        if (shader && shader.uniforms) {
            shader.uniforms.uResolution = [app.screen.width, app.screen.height];
            mesh.position.set(0, 0);
            mesh.width = app.screen.width;
            mesh.height = app.screen.height;
        }
    }

    return {
        mesh,
        update: updateShader,
        resize: resizeShader,
        destroy: () => {
            mesh.destroy();
        }
    };
}
