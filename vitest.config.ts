import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';

export default defineConfig({
    plugins: [sveltekit()],
    test: {
        include: ['src/**/*.{test,spec}.{js,ts}'],
        environment: 'jsdom',
        setupFiles: ['./src/test/setup.ts'],
        globals: true,
        coverage: {
            reporter: ['text', 'json', 'html'],
            exclude: [
                'node_modules/',
                'src/test/',
                '**/*.d.ts',
                '**/*.config.*',
                'build/',
                '.svelte-kit/'
            ],
            thresholds: {
                // Current threshold set to 30% - gradually increase as test coverage improves
                // Goal: 80% coverage across all metrics
                global: {
                    branches: 30,
                    functions: 30,
                    lines: 30,
                    statements: 30
                }
            }
        }
    }
});
